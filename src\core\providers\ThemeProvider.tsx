import { Css<PERSON><PERSON><PERSON>, Theme<PERSON><PERSON>ider as MuiThemeProvider, createTheme } from '@mui/material'
import { type PropsWithChildren, useMemo } from 'react'
import { useAppStore } from '../store'

export default function ThemeProvider({ children }: PropsWithChildren) {
    const mode = useAppStore((s) => s.themeMode)
    const theme = useMemo(
        () =>
            createTheme({
                spacing: 8,
                palette: {
                    mode,
                    primary: {
                        main: mode === 'light' ? '#0B5FFF' : '#7DA3FF',
                        contrastText: mode === 'light' ? '#FFFFFF' : '#000000',
                    },
                    secondary: {
                        main: mode === 'light' ? '#6C757D' : '#AAB2B8',
                        contrastText: mode === 'light' ? '#FFFFFF' : '#000000',
                    },
                    background: {
                        default: mode === 'light' ? '#F7F8FA' : '#0E1116',
                        paper: mode === 'light' ? '#FFFFFF' : '#151A21',
                    },
                },
                shape: { borderRadius: 10 },
                components: {
                    MuiButton: {
                        styleOverrides: {
                            root: {
                                textTransform: 'none',
                                borderRadius: 2,
                                paddingInline: 16,
                                paddingBlock: 8,
                            },
                            contained: {
                                color: mode === 'light' ? '#FFFFFF' : '#000000',
                            },
                            outlined: {
                                color: mode === 'light' ? '#0B5FFF' : '#7DA3FF',
                                borderColor: mode === 'light' ? '#0B5FFF' : '#7DA3FF',
                            },
                            text: {
                                color: mode === 'light' ? '#0B5FFF' : '#7DA3FF',
                            },
                        },
                        defaultProps: {
                            // This ensures variant="contained" buttons use proper contrast
                            variant: 'contained',
                        },
                    },
                    MuiPaper: {
                        defaultProps: { elevation: 0 },
                        styleOverrides: { root: { padding: 16 } },
                    },
                    MuiAppBar: {
                        styleOverrides: { root: { borderBottom: mode === 'light' ? '1px solid #E5E7EB' : '1px solid #1F2937' } },
                    },
                    MuiTableCell: {
                        styleOverrides: { root: { paddingBlock: 8, paddingInline: 12 } },
                    },
                },
            }),
        [mode],
    )
    return (
        <MuiThemeProvider theme={theme}>
            <CssBaseline />
            {children}
        </MuiThemeProvider>
    )
}
