import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Theme<PERSON><PERSON><PERSON> as MuiThemeProvider, createTheme } from '@mui/material'
import { type PropsWithChildren, useMemo } from 'react'
import { useAppStore } from '../store'

export default function ThemeProvider({ children }: PropsWithChildren) {
    const mode = useAppStore((s) => s.themeMode)
    const theme = useMemo(
        () =>
            createTheme({
                spacing: 8,
                palette: {
                    mode,
                },
                shape: { borderRadius: 10 },
                components: {
                    MuiPaper: {
                        defaultProps: { elevation: 0 },
                        styleOverrides: {
                            root: {
                                padding: 16,
                                border: mode === 'light' ? '1px solid #E5E7EB' : '1px solid #1F2937',
                            }
                        },
                    },
                },
            }),
        [mode],
    )
    return (
        <MuiThemeProvider theme={theme}>
            <CssBaseline />
            {children}
        </MuiThemeProvider>
    )
}
