import { Paper, Typography, Button, FormControl, InputLabel, MenuItem, Select } from '@mui/material'
import { useRef, useState } from 'react'
import { useTranslation } from 'react-i18next'
import { useAppStore } from '../core/store'

export default function SettingsPage() {
	const { t, i18n } = useTranslation()
	const inputRef = useRef<HTMLInputElement | null>(null)
	const [fileName, setFileName] = useState<string>('')
	const locale = useAppStore((s) => s.locale)
	const setLocale = useAppStore((s) => s.setLocale)

	function handleLocaleChange(value: 'en' | 'ja') {
		setLocale(value)
		i18n.changeLanguage(value)
	}

	return (
		<Paper className="p-4 space-y-4">
			<Typography variant="h5">{t('nav.settings')}</Typography>

			<FormControl size="small" sx={{ minWidth: 200 }}>
				<InputLabel id="locale-label">Language</InputLabel>
				<Select labelId="locale-label" label="Language" value={locale} onChange={(e) => handleLocaleChange(e.target.value as 'en' | 'ja')}>
					<MenuItem value="en">English</MenuItem>
					<MenuItem value="ja">日本語</MenuItem>
				</Select>
			</FormControl>

			<div className="flex items-center gap-3">
				<input
					ref={inputRef}
					type="file"
					accept="application/json"
					className="hidden"
					onChange={(e) => setFileName(e.target.files?.[0]?.name ?? '')}
				/>
				<Button variant="contained" onClick={() => inputRef.current?.click()}>{t('settings.import', { defaultValue: 'Import JSON' })}</Button>
				{fileName && <Typography variant="body2">{t('settings.selected', { defaultValue: 'Selected' })}: {fileName}</Typography>}
			</div>
		</Paper>
	)
}
