import { Suspense, lazy, useEffect, useState } from 'react'
import { Box, Pagination, Paper, Typography, useTheme } from '@mui/material'
import DataTable from '../app/dashboard/components/DataTable'
import type { Task } from '../app/dashboard/types/Task'
import { fetchTasks } from '../app/dashboard/services/tasks'

const LazyChartCard = lazy(() => import('../app/dashboard/components/ChartCard'))

const elementsPerPagePortrait = 4
const elementsPerPageLandscape = 1

export default function DashboardPage() {
	const theme = useTheme()
	const [page, setPage] = useState(1)
	const [isLandscape, setIsLandscape] = useState(() => matchMedia('(orientation: landscape)').matches)
	const [tasks, setTasks] = useState<Task[]>([])
	const [loading, setLoading] = useState(true)
	const [error, setError] = useState<string | null>(null)

	useEffect(() => {
		const controller = new AbortController()
		setLoading(true)
		setError(null)
		fetchTasks(controller.signal)
			.then((data) => setTasks(data))
			.catch((e) => {
				const isAbort = (e && typeof e === 'object' && 'name' in e && (e as any).name === 'AbortError') || (e instanceof DOMException && e.name === 'AbortError')
				if (!isAbort) {
					setError(e instanceof Error ? e.message : 'Unknown error')
				}
			})
			.finally(() => setLoading(false))
		return () => controller.abort()
	}, [])

	useEffect(() => {
		const mq = matchMedia('(orientation: landscape)')
		const handler = () => setIsLandscape(mq.matches)
		mq.addEventListener?.('change', handler)
		return () => mq.removeEventListener?.('change', handler)
	}, [])

	const elementsPerPage = isLandscape ? elementsPerPageLandscape : elementsPerPagePortrait
	const totalElements = 12
	const totalPages = Math.ceil(totalElements / elementsPerPage)
	const startIndex = (page - 1) * elementsPerPage
	const pageItems = Array.from({ length: Math.min(elementsPerPage, totalElements - startIndex) }, (_, i) => startIndex + i)

	function ElementCard() {
		const initial = tasks[0] ?? null
		const [selected, setSelected] = useState<Task | null>(initial)
		useEffect(() => {
			if (!selected && tasks.length > 0) setSelected(tasks[0])
		}, [tasks, selected])
		return (
			<Paper
				variant="outlined"
				className="space-y-4 w-full"
				sx={{ backgroundColor: theme.palette.mode === 'light' ? '#ffffff' : '#12161c' }}
			>
				{/* <Typography variant="h6">Element {elementIndex + 1}</Typography> */}
				<div className="grid grid-cols-1 gap-4 md:grid-cols-[70%_30%]">
					<div className="min-h-[320px]">
						<DataTable
							rows={tasks}
							selectedRowId={selected?.id ?? null}
							onSelect={(row) => setSelected(row)}
						/>
					</div>
					<div className="min-h-[320px]">
						<Suspense fallback={<div className="p-4">Loading chart…</div>}>
							<LazyChartCard label={selected?.name ?? '—'} value={selected?.progress ?? 0} />
						</Suspense>
					</div>
				</div>
			</Paper>
		)
	}

	if (loading) {
		return (
			<Box className="p-4 w-full">
				<Typography>Loading…</Typography>
			</Box>
		)
	}
	if (error) {
		return (
			<Box className="p-4 w-full">
				<Typography color="error">{error}</Typography>
			</Box>
		)
	}

	// Estimate heights for sticky header & bar to compute scrollable area height
	const headerH = 40
	const stickyBarH = 56
	const verticalPad = 16
	const maxHCalc = `calc(100vh - ${headerH + stickyBarH + verticalPad}px)`

	return (
		<Box className="space-y-4 w-full">
			<Box
				className="sticky top-0 z-10"
				sx={{
					backgroundColor: theme.palette.background.paper,
					borderBottom: `1px solid ${theme.palette.divider}`,
					boxShadow: theme.palette.mode === 'light' ? '0 1px 2px rgba(0,0,0,0.04)' : '0 1px 2px rgba(0,0,0,0.5)',
					pt: 1,
					pb: 1,
				}}
			>
				<div className="flex justify-center">
					<Pagination count={totalPages} page={page} onChange={(_, p) => setPage(p)} color="primary" />
				</div>
			</Box>

			<Box sx={{ overflow: 'auto', maxHeight: { xs: maxHCalc, md: maxHCalc }, pb: 3 }}>
				<div className="grid grid-cols-1 gap-4 w-full xl:max-w-[1600px] mx-auto">
					{pageItems.map((idx) => (
						<div key={idx} className="col-span-1 w-full">
							<ElementCard />
						</div>
					))}
					<div className="h-6" />
				</div>
			</Box>
		</Box>
	)
}
