import { Paper, Typography } from '@mui/material'
import { Bar, BarChart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'

export default function ChartCard({ label, value }: { label: string; value: number }) {
	const data = [{ name: label, value }]
	return (
		<Paper className="p-4">
			<Typography variant="subtitle1" className="mb-2">
				Value
			</Typography>
			<div style={{ width: '50%', height: 280 }}>
				<ResponsiveContainer>
					<BarChart data={data} margin={{ top: 30, right: 20, left: 10, bottom: 5 }}>
						<CartesianGrid strokeDasharray="3 3" />
						<XAxis dataKey="name" />
						<YAxis domain={[0, 100]} ticks={[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]} interval={0}/>
						<Tooltip />
						<Bar dataKey="value" fill="#8884d8" />
					</BarChart>
				</ResponsiveContainer>
			</div>
		</Paper>
	)
}
