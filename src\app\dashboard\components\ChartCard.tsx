import { Paper, Typography, useTheme } from '@mui/material'
import { <PERSON>, Bar<PERSON>hart, CartesianGrid, ResponsiveContainer, Tooltip, XAxis, YAxis } from 'recharts'

export default function ChartCard({ label, value }: { label: string; value: number }) {
    const theme = useTheme()
    const data = [{ name: label, value }]
    return (
        <Paper className="p-4">
            <Typography variant="subtitle1" className="mb-2">
                {theme.palette.mode === 'light' ? 'Value' : 'Value'}
            </Typography>
            <div style={{ width: '50%', height: 280 }}>
                <ResponsiveContainer>
                    <BarChart data={data} margin={{ top: 30, right: 20, left: 10, bottom: 5 }}>
                        <CartesianGrid stroke={theme.palette.divider} strokeDasharray="3 3" />
                        <XAxis dataKey="name" stroke={theme.palette.text.secondary} />
                        <YAxis domain={[0, 100]} ticks={[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100]} interval={0} stroke={theme.palette.text.secondary} />
                        <Tooltip contentStyle={{ background: theme.palette.background.paper, border: `1px solid ${theme.palette.divider}` }} />
                        <Bar dataKey="value" fill={theme.palette.primary.main} />
                    </BarChart>
                </ResponsiveContainer>
            </div>
        </Paper>
    )
}
