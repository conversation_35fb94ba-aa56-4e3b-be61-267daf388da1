import i18n from 'i18next'
import { initReactI18next } from 'react-i18next'
import en from './locales/en.json'
import ja from './locales/ja.json'

export function initI18n(locale: 'en' | 'ja' = 'en') {
	i18n
		.use(initReactI18next)
		.init({
			resources: { en: { translation: en }, ja: { translation: ja } },
			lng: locale,
			fallbackLng: 'en',
			interpolation: { escapeValue: false },
		})
	return i18n
}
