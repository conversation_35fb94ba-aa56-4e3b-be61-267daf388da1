import {
	Paper,
	Table,
	TableBody,
	TableCell,
	TableContainer,
	TableHead,
	TableRow,
} from '@mui/material'
import type { Task } from '../../../models/Task'
import { useTranslation } from 'react-i18next'

export default function DataTable({
	rows,
	selectedRowId,
	onSelect,
}: {
	rows: Task[]
	selectedRowId?: number | null
	onSelect?: (row: Task) => void
}) {
	const { t } = useTranslation()
	return (
		<TableContainer component={Paper} sx={{ maxHeight: 480, p: 0, position: 'relative' }}>
			<Table stickyHeader size="small">
				<TableHead>
					{/* Top-level headers */}
					<TableRow>
						<TableCell
							colSpan={4}
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.name')}
						</TableCell>
						<TableCell
							colSpan={3}
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.planned')}
						</TableCell>
						<TableCell
							colSpan={3}
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.actual')}
						</TableCell>
					</TableRow>
					{/* Sub-headers */}
					<TableRow>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.no')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.shippingDate')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.vangp')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.deliveryTime')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.start')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.end')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.duration')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.start')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.end')}
						</TableCell>
						<TableCell
							align="center"
							sx={{
								position: 'sticky',
								backgroundColor: 'background.paper',
								borderBottom: '1px solid',
								borderColor: 'divider',
							}}
						>
							{t('table.duration')}
						</TableCell>
					</TableRow>
				</TableHead>
				<TableBody sx={{ '& td': { backgroundColor: 'background.default' } }}>
					{rows.map((row) => {
						const isSelected = row.id === selectedRowId
						return (
							<TableRow
								key={row.id}
								hover
								selected={isSelected}
								onClick={() => onSelect?.(row)}
								sx={{ cursor: onSelect ? 'pointer' : 'default' }}
							>
								<TableCell align="center">{row.no}</TableCell>
								<TableCell align="center">{row.shppingDate}</TableCell>
								<TableCell align="center">{row.vangp}</TableCell>
								<TableCell align="center">{row.deliveryTime}</TableCell>
								<TableCell align="center">{row.plannedStart}</TableCell>
								<TableCell align="center">{row.plannedEnd}</TableCell>
								<TableCell align="center">{row.plannedDuration}</TableCell>
								<TableCell align="center">{row.actualStart}</TableCell>
								<TableCell align="center">{row.actualEnd}</TableCell>
								<TableCell align="center">{row.actualDuration}</TableCell>
							</TableRow>
						)
					})}
				</TableBody>
			</Table>
		</TableContainer>
	)
}
