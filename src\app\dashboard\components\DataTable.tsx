import { Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material'
import type { Task } from '../types/Task'

export default function DataTable({ rows, selectedRowId, onSelect }: { rows: Task[]; selectedRowId?: number | null; onSelect?: (row: Task) => void }) {
    const topFirst = 0
    const headerFirstH = 40
    const topSecond = headerFirstH + 1
    const zTop = 10
    const zSecond = 9
    return (
        <TableContainer component={Paper} sx={{ maxHeight: 480, p: 0, position: 'relative' }}>
            <Table stickyHeader size="small">
                <TableHead>
                    {/* Top-level headers */}
                    <TableRow sx={{ height: headerFirstH }}>
                        <TableCell rowSpan={2} sx={{ position: 'sticky', top: topFirst, zIndex: zTop, backgroundColor: 'background.paper', borderBottom: '1px solid', borderColor: 'divider' }}>Name</TableCell>
                        <TableCell colSpan={3} align="center" sx={{ position: 'sticky', top: topFirst, zIndex: zTop, backgroundColor: 'background.paper', borderBottom: '1px solid', borderColor: 'divider' }}>Planned</TableCell>
                        <TableCell colSpan={3} align="center" sx={{ position: 'sticky', top: topFirst, zIndex: zTop, backgroundColor: 'background.paper', borderBottom: '1px solid', borderColor: 'divider' }}>Actual</TableCell>
                    </TableRow>
                    {/* Sub-headers */}
                    <TableRow>
                        <TableCell align="center" sx={{ position: 'sticky', top: topSecond, zIndex: zSecond, backgroundColor: 'background.paper', borderBottom: '1px solid', borderColor: 'divider' }}>Start</TableCell>
                        <TableCell align="center" sx={{ position: 'sticky', top: topSecond, zIndex: zSecond, backgroundColor: 'background.paper', borderBottom: '1px solid', borderColor: 'divider' }}>End</TableCell>
                        <TableCell align="center" sx={{ position: 'sticky', top: topSecond, zIndex: zSecond, backgroundColor: 'background.paper', borderBottom: '1px solid', borderColor: 'divider' }}>Duration</TableCell>
                        <TableCell align="center" sx={{ position: 'sticky', top: topSecond, zIndex: zSecond, backgroundColor: 'background.paper', borderBottom: '1px solid', borderColor: 'divider' }}>Start</TableCell>
                        <TableCell align="center" sx={{ position: 'sticky', top: topSecond, zIndex: zSecond, backgroundColor: 'background.paper', borderBottom: '1px solid', borderColor: 'divider' }}>End</TableCell>
                        <TableCell align="center" sx={{ position: 'sticky', top: topSecond, zIndex: zSecond, backgroundColor: 'background.paper', borderBottom: '1px solid', borderColor: 'divider' }}>Duration</TableCell>
                    </TableRow>
                </TableHead>
                <TableBody sx={{ '& td': { backgroundColor: 'background.default' } }}>
                    {rows.map((row) => {
                        const isSelected = row.id === selectedRowId
                        return (
                            <TableRow key={row.id} hover selected={isSelected} onClick={() => onSelect?.(row)} sx={{ cursor: onSelect ? 'pointer' : 'default' }}>
                                <TableCell>{row.name}</TableCell>
                                <TableCell align="center">{row.plannedStart}</TableCell>
                                <TableCell align="center">{row.plannedEnd}</TableCell>
                                <TableCell align="center">{row.plannedDuration}</TableCell>
                                <TableCell align="center">{row.actualStart}</TableCell>
                                <TableCell align="center">{row.actualEnd}</TableCell>
                                <TableCell align="center">{row.actualDuration}</TableCell>
                            </TableRow>
                        )
                    })}
                </TableBody>
            </Table>
        </TableContainer>
    )
}
