import { Suspense, lazy, useEffect, useState } from 'react'
import { Paper } from '@mui/material'
import DataTable from './DataTable'
import type { Task } from '../../../models/Task'

const LazyChartCard = lazy(() => import('./ChartCard'))

interface ElementCardProps {
	tasks: Task[]
}

export default function ElementCard({ tasks }: ElementCardProps) {
	const initial = tasks[0] ?? null
	const [selected, setSelected] = useState<Task | null>(initial)

	useEffect(() => {
		if (!selected && tasks.length > 0) setSelected(tasks[0])
	}, [tasks, selected])

	return (
		<Paper variant="outlined" className="space-y-4 w-full">
			<div className="grid grid-cols-1 gap-2 md:grid-cols-[70%_30%]">
				<div className="min-h-[320px]">
					<DataTable
						rows={tasks}
						selectedRowId={selected?.id ?? null}
						onSelect={(row) => setSelected(row)}
					/>
				</div>
				<div className="min-h-[320px]">
					<Suspense fallback={<div className="p-4">Loading chart…</div>}>
						<LazyChartCard selectedTask={selected} />
					</Suspense>
				</div>
			</div>
		</Paper>
	)
}
