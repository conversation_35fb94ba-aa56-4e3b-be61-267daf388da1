import { create } from 'zustand'

interface AppState {
	locale: 'en' | 'ja'
	setLocale: (l: 'en' | 'ja') => void
	themeMode: 'light' | 'dark'
	setThemeMode: (m: 'light' | 'dark') => void
	toggleThemeMode: () => void
}

export const useAppStore = create<AppState>((set, get) => ({
	locale: 'en',
	setLocale: (locale) => set({ locale }),
	themeMode: 'light',
	setThemeMode: (themeMode) => set({ themeMode }),
	toggleThemeMode: () => set({ themeMode: get().themeMode === 'light' ? 'dark' : 'light' }),
}))
