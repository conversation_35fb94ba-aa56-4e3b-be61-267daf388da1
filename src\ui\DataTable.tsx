import { Paper, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from '@mui/material'
import type { Task } from '../models/Task'

export default function DataTable({ rows, selectedRowId, onSelect }: { rows: Task[]; selectedRowId?: number | null; onSelect?: (row: Task) => void }) {
	return (
		<TableContainer component={Paper} sx={{ maxHeight: 480 }}>
			<Table stickyHeader size="small">
				<TableHead>
					{/* Top-level headers */}
					<TableRow>
						<TableCell rowSpan={2}>Name</TableCell>
						<TableCell colSpan={3} align="center">Planned</TableCell>
						<TableCell colSpan={3} align="center">Actual</TableCell>
					</TableRow>
					{/* Sub-headers */}
					<TableRow>
						<TableCell align="center">Start</TableCell>
						<TableCell align="center">End</TableCell>
						<TableCell align="center">Duration</TableCell>
						<TableCell align="center">Start</TableCell>
						<TableCell align="center">End</TableCell>
						<TableCell align="center">Duration</TableCell>
					</TableRow>
				</TableHead>
				<TableBody>
					{rows.map((row) => {
						const isSelected = row.id === selectedRowId
						return (
							<TableRow key={row.id} hover selected={isSelected} onClick={() => onSelect?.(row)} sx={{ cursor: onSelect ? 'pointer' : 'default' }}>
								<TableCell>{row.name}</TableCell>
								<TableCell align="center">{row.plannedStart}</TableCell>
								<TableCell align="center">{row.plannedEnd}</TableCell>
								<TableCell align="center">{row.plannedDuration}</TableCell>
								<TableCell align="center">{row.actualStart}</TableCell>
								<TableCell align="center">{row.actualEnd}</TableCell>
								<TableCell align="center">{row.actualDuration}</TableCell>
							</TableRow>
						)
					})}
				</TableBody>
			</Table>
		</TableContainer>
	)
}
