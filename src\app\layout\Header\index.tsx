import { App<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, IconButton } from '@mui/material'
import { Link as RouterLink, useLocation } from 'react-router-dom'
import { useAppStore } from '../../../core/store'
import { useTranslation } from 'react-i18next'
import LightModeIcon from '@mui/icons-material/LightMode'
import DarkModeIcon from '@mui/icons-material/DarkMode'

export default function Header() {
    const { t } = useTranslation()
    const location = useLocation()
    const isSettings = location.pathname.startsWith('/settings')
    const themeMode = useAppStore((s) => s.themeMode)
    const toggleThemeMode = useAppStore((s) => s.toggleThemeMode)
    return (
        <AppBar position="sticky" sx={{ top: 0 }}>
            <Toolbar variant="dense" sx={{ minHeight: 40 }}> {/* reduce header height */}
                <Typography variant="h6" sx={{ flexGrow: 1, fontSize: 16 }}>
                    VMApp
                </Typography>
                <Button color="inherit" component={RouterLink} to="/" disabled={!isSettings}>
                    {t('nav.dashboard')}
                </Button>
                <Button color="inherit" component={RouterLink} to="/settings" disabled={isSettings}>
                    {t('nav.settings')}
                </Button>
                <IconButton color="inherit" onClick={toggleThemeMode} sx={{ ml: 1 }} aria-label="toggle theme">
                    {themeMode === 'light' ? <DarkModeIcon fontSize="small" /> : <LightModeIcon fontSize="small" />}
                </IconButton>
            </Toolbar>
        </AppBar>
    )
}
