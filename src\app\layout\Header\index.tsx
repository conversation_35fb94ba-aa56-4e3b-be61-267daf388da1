import { App<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button, IconButton, useTheme } from '@mui/material'
import { Link as RouterLink } from 'react-router-dom'
import { useAppStore } from '../../../core/store'
import { useTranslation } from 'react-i18next'
import LightModeIcon from '@mui/icons-material/LightMode'
import DarkModeIcon from '@mui/icons-material/DarkMode'

export default function Header() {
    const { t } = useTranslation()
    const themeMode = useAppStore((s) => s.themeMode)
    const toggleThemeMode = useAppStore((s) => s.toggleThemeMode)
    const theme = useTheme()

    return (
        <AppBar position="sticky" sx={{ top: 0 }}>
            <Toolbar variant="dense" sx={{ minHeight: 40 }}> {/* reduce header height */}
                <Typography variant="h6" sx={{ flexGrow: 1, fontSize: 16 }}>
                    VMApp
                </Typography>
                <Button
                    component={RouterLink}
                    to="/"
                    color='inherit'
                >
                    {t('nav.dashboard')}
                </Button>
                <Button
                    component={RouterLink}
                    to="/settings"
                    color='inherit'
                >
                    {t('nav.settings')}
                </Button>
                <IconButton
                    color="inherit"
                    onClick={toggleThemeMode}
                    sx={{
                        ml: 1,
                        color: theme.palette.mode === 'light' ? '#FFFFFF' : '#E0E0E0',
                        '&:hover': {
                            backgroundColor: theme.palette.mode === 'light'
                                ? 'rgba(255, 255, 255, 0.1)'
                                : 'rgba(255, 255, 255, 0.05)',
                        }
                    }}
                    aria-label="toggle theme"
                >
                    {themeMode === 'light' ? <DarkModeIcon fontSize="small" /> : <LightModeIcon fontSize="small" />}
                </IconButton>
            </Toolbar>
        </AppBar>
    )
}
