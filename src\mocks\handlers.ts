import { http, HttpResponse } from 'msw'
import type { Task } from '../models/Task'

function randomTime(baseHour: number) {
	const hour = baseHour + Math.floor(Math.random() * 3)
	const minute = Math.floor(Math.random() * 60)
	return `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
}

function makeTasks(n: number): Task[] {
	return Array.from({ length: n }, (_, i) => {
		const plannedStart = randomTime(8)
		const plannedEnd = randomTime(12)
		const plannedDuration = `${Math.floor(Math.random() * 4 + 1)}h`
		const actualStart = randomTime(9)
		const actualEnd = randomTime(13)
		const actualDuration = `${Math.floor(Math.random() * 5 + 1)}h`
		const progress = Math.round(Math.random() * 100)
		return {
			id: i + 1,
			name: `Task ${i + 1}`,
			plannedStart,
			plannedEnd,
			plannedDuration,
			actualStart,
			actualEnd,
			actualDuration,
			progress,
		}
	})
}

export const handlers = [
	http.get('/api/tasks.json', async () => {
		const data = makeTasks(40)
		return HttpResponse.json(data)
	}),
]
